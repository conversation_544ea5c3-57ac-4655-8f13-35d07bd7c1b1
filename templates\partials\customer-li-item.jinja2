<div>
  <style>
    me {
        width: 100%;
        height: auto;
    }
  </style>

  <form data-signals="{_{{ namealwayschange }}_customer_submit_button_disable:false}" data-on-submit="$_{{ namealwayschange }}_customer_submit_button_disable = true;@post('/customers', {contentType: 'form'})">
    <style>
      me {
          animation: cardAppear 0.4s ease-out;
      }

      @keyframes cardAppear {
          from {
              opacity: 0;
              transform: translateY(-40px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
    </style>
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="customer_id" value="{{ customer_id }}">



    {# INPUT #}
    <div>
      <style>
          me {
              display: flex;
              flex-direction: column-reverse;
              padding-top: 64px;
              padding-bottom: 20px;
              flex-basis: 100%;
          }

          me .input-group__error {
              color: #ae2300;
              display: block;
              position: relative;
              visibility: hidden;
              opacity: 0;
              margin-left: 10px;
              margin-top: 1px;
              margin-bottom: -52px;
              font-family: 'Noto Serif', serif;
              font-size: 14px;
              transition: all 0.3s ease-out;
          }

          me input {
              font-family: 'Noto Sans', sans-serif;
              font-size: 18px;
              color: black;
              height: 29px;
              border: 0;
              z-index: 1;
              background-color: transparent;
              border-bottom: 1px solid var(--color-input-lines);

              &:focus {
                  outline: 0;
                  border-bottom: 1px solid var(--color-input-lines);

                  &+.input-label {
                      font-family: 'Noto Serif', serif;
                      font-style: italic;
                      font-size: 14px;
                      color: var(--color-input-lines);
                      transform: translateY(-1.5rem);
                  }
              }

              &:valid {
                  border-bottom: 1px solid hsl(55, 96%, 38%);

                  &+.input-label {
                      font-family: 'Noto Serif', serif;
                      font-style: italic;
                      font-size: 14px;
                      color: var(--color-input-lines);
                      transform: translateY(-1.5rem);
                  }
              }

              &:not(:placeholder-shown):invalid {
                  border-bottom: 1px solid #ff3300;

                  &+.input-label {
                      font-family: 'Noto Serif', serif;
                      font-style: italic;
                      font-size: 14px;
                      color: var(--color-input-lines);
                      transform: translateY(-1.5rem);
                  }
              }

              &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
                  visibility: visible;
                  opacity: 1;
              }
          }
      </style>
      <input pattern=".{4,}" placeholder="" type="text" value="{{ customer_name | default('') }}" name="name" required />
      <label class="input-label">
        <style>
          me {
              color: black;
              position: relative !important;
              transition: .15s ease;
              margin-bottom: -28px;
              display: block !important;
          }
          me::before {
              content: none !important;
              display: none !important;
          }
        </style>
        Name
      </label>

      <span id="{{ namealwayschange }}-errordiv" class="input-group__error">{{ errormessage }}</span>
    </div>


    {# TEXTAREA #}
    <div>
      <style>
        me {
            display: flex;
            flex-direction: column;
            padding-top: 14px;
            padding-bottom: 0px;
            flex-basis: 100%;
        }

        me label {
            color: black;
            margin-bottom: 4px;
            font-family: 'Noto Sans', sans-serif;
            font-size: 18px;
            font-weight: 300;
        }

        me textarea {
          resize: none;
          padding: 12px 18px;
          height: 158px;
          border: 1px solid var(--color-input-lines);
          border-radius: 8px;
          background-color: transparent;
          font-family: 'Noto Sans', sans-serif;
          font-size: 18xp;
          font-weight: 300;
          transition: border-color 0.2s ease;
          overflow: hidden;
          line-height: 1.5;
        }

        me textarea:focus {
          outline: none;
          border: 1px solid #006400;
        }
      </style>
      <label>Info</label>
      <textarea name="info" onkeydown="if(this.value.split('\n').length > 5 && event.key === 'Enter') event.preventDefault();" onpaste="setTimeout(() => { const lines = this.value.split('\n'); if(lines.length > 6) this.value = lines.slice(0, 6).join('\n'); }, 0);">{{ customer_info | default('') }}</textarea>
    </div>

    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_{{ namealwayschange }}_customer_submit_button_disable">
      <style>
          me {
              height: 40px;
              margin-top: 14px;
              margin-bottom: 10px;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-text-dark);
              border: 1px solid var(--color-text-dark);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
          }

          me:hover {
              background-color: var(--color-background-dark);
              color: var(--color-text-bright);
          }

          me:disabled {
              background-color: rgb(72, 72, 72);
              opacity: 0.4;
              padding: 0px 0px;
          }

          me:disabled .button-spinner {
              display: inline;
              margin-top: 4px;
              margin-bottom: 0px;
          }

          me:disabled .button-text {
              display: none;
          }

          me .button-spinner {
              display: none;
              width: 30px;
              height: 30px;
          }
      </style>
      <span class="button-text">Save</span>
      <img class="button-spinner"
            src="/static/images/tube-spinner.svg"
            alt="spinning..." />
    </button>

  </form>

  {# Delete Button #}
  {% if customer_id %}
  <form data-on-submit="@post('/customers', {contentType: 'form'})">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="customer_id" value="{{ customer_id }}">
    <button type="submit" onclick="return confirm('Are you sure you want to delete this customer?')">
      <style>
          me {
              height: 40px;
              margin-top: 14px;
              margin-bottom: 10px;
              width: 100%;
              display: block;
              background-color: transparent;
              color: rgb(197, 0, 0);
              border: 1px solid rgb(197, 0, 0);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
          }

          me:hover {
              background-color: rgb(90, 0, 0);
              color: var(--color-text-bright);
          }
      </style>
      Delete
    </button>
  </form>
  {% endif %}

</div>
