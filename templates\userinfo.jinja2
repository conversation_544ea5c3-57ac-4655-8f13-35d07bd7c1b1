{% extends "base.jinja2" %}

{% block title %}User Info{% endblock %}
{% block content %}

<div>
  <style>
      me {
        position: relative;
        width: 100%;
      }
  </style>

  {# X BUTTON (TOP RIGHT) #}
  <div onclick="location.href='/calculations'">
    <style>
        me {
            position: absolute;
            top: -30px;
            right: clamp(-150px, -31%, -18px);
            width: 32px;
            height: 32px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

          me .close-x {
              width: 26px;
              height: 2px;
              background-color: var(--color-text-dark);
              position: relative;
              transform: rotate(45deg);
              transition: all 0.3s ease;
          }

          me .close-x::after {
              content: '';
              position: absolute;
              width: 26px;
              height: 2px;
              background-color: var(--color-text-dark);
              left: 0;
              top: 0;
              transform: rotate(-90deg);
              transition: all 0.3s ease;
          }

          me:hover .close-x,
          me:hover .close-x::after {
              background-color: #209200;
          }
      </style>
      <div class="close-x"></div>
    </div>

    {# TITLE DOUBLE #}
    <div>
      <style>
          me {
              width: 100%;
              text-align: center;
              padding-bottom: 8px;
          }

          me hr {
              display: block;
              height: 1px;
              border: 0;
              border-top: 1px solid #a8a8a8;
              margin-top: 10;
              padding: 0;
          }
      </style>
      <div>
        User Info
        <style>
            me {
                padding-bottom: 28px;
                font-family: 'Noto Serif', serif;
                font-size: 30px;
                font-weight: 400;
                font-stretch: semi-condensed;
                font-style: italic;
            }
        </style>
      </div>
      <div>
        <style>
            me {
                padding-bottom: 2px;
                font-family: "Noto Sans", sans-serif;
                font-size: 20px;
                font-weight: 300;
                font-stretch: semi-condensed;
                font-style: normal;
            }
        </style>
        {{ user.email }}
      </div>
      <hr />
      <div>
        <style>
            me {
              padding-top: 2px;
              padding-bottom: 0px;
              font-family: "Noto Sans", sans-serif;
              font-size: 18px;
              font-weight: 300;
              font-stretch: semi-condensed;
              font-style: normal;
            }
        </style>
        This info will be showed on the pdf report.
      </div>
    </div>

  <form data-signals="{_users_submit_button_disable:false}" data-on-submit="$_users_submit_button_disable = true;@post('/userinfo', {contentType: 'form'})">
    {{ render_partial('partials/forms-input.jinja2', namealwayschange='userinfoname', label='Name', type = 'text', pattern=".{4,}", errormessage="Name must be at least 4 characters", value=user_settings.name) }}
    <div>
      <style>
        me {
            display: flex;
            flex-direction: column;
            padding-top: 34px;
            padding-bottom: 0px;
            flex-basis: 100%;
        }

        me > label {
            color: black;
            margin-bottom: 4px;
            font-family: 'Noto Sans', sans-serif;
            font-size: 18px;
            font-weight: 300;
        }

        me textarea {
          resize: none;
          padding: 12px 18px;
          height: 158px;
          border: 1px solid var(--color-input-lines);
          border-radius: 8px;
          background-color: transparent;
          font-family: 'Noto Sans', sans-serif;
          font-size: 18px;
          font-weight: 300;
          transition: border-color 0.2s ease;
          overflow: hidden;
          line-height: 1.5;
        }

        me textarea:focus {
          outline: none;
          border: 1px solid #006400;
        }
      </style>
      <label>Info</label>
      <textarea name="userinfo" onkeydown="if(this.value.split('\n').length > 5 && event.key === 'Enter') event.preventDefault();" onpaste="setTimeout(() => { const lines = this.value.split('\n'); if(lines.length > 6) this.value = lines.slice(0, 6).join('\n'); }, 0);">{{ user_settings.info }}</textarea>
    </div>



    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_users_submit_button_disable">
      <style>
          me {
              height: 40px;
              margin-top: 3rem;
              margin-bottom: 3rem;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-text-dark);
              border: 1px solid var(--color-text-dark);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
          }

          me:hover {
              background-color: var(--color-background-dark);
              color: var(--color-text-bright);
          }

          me:disabled {
              background-color: rgb(72, 72, 72);
              opacity: 0.4;
              padding: 0px 0px;
          }

          me:disabled .button-spinner {
              display: inline;
              margin-top: 4px;
              margin-bottom: 0px;
          }

          me:disabled .button-text {
              display: none;
          }

          me .button-spinner {
              display: none;
              width: 30px;
              height: 30px;
          }
      </style>
      <span class="button-text">Save & Exit</span>
      <img class="button-spinner"
            src="{{ url_for('static', path='/images/tube-spinner.svg') }}"
            alt="spinning..." />
    </button>

  </form>

  <div id="errordiv"></div>

</div>

{% endblock content %}

