<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 94px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me select {
          font-family: 'Noto Sans', sans-serif;
          font-size: 14px;
          color: black;
          height: 29px;
          border: 0;
          z-index: 1;
          background-color: transparent;
          border-bottom: 1px solid var(--color-input-lines);
          font-size: 1.125rem;
          background: url(/static/images/icons8-arrow-down-64.png) no-repeat right var(--color-background-bright);
          background-size: 26px 24px;
          padding-right: 30px;

          &:focus {
              outline: 0;
              border-bottom: 1px solid var(--color-input-lines);

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-input-lines);
                  transform: translateY(-1.5rem);
              }
          }

          &:valid {
              border-bottom: 1px solid hsl(55, 96%, 38%);

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-input-lines);
                  transform: translateY(-1.5rem);
              }
          }
      }
  </style>
  <div class="floating-label">
    <select class="floating-select" onclick="this.setAttribute('value', this.value);" value="">
      <option value=""></option>
      {% for option in optionslist %}
        {% if option is mapping %}
          <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
        {% else %}
          <option value="{{ option }}">{{ option }}</option>
        {% endif %}
      {% endfor %}
    </select>
    <label>{{ label }}</label>
  </div>

</div>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const selects = document.querySelectorAll(".floating-select");
    selects.forEach(select => {
      select.setAttribute("value", select.value);
    });
  });
</script>