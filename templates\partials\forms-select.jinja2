<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 65px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me select {
          all: unset;
          padding-right: 15px;
          border-bottom: 1px solid;
          border-bottom-color: var(--color-input-lines);
          background: url(/static/images/icons8-arrow-down-64.png) no-repeat right var(--color-background-bright);
          height: 28px;
          background-repeat: no-repeat;
          background-size: 26px 24px;
      }

      me .floating-label { 
        position:relative; 
        margin-bottom:20px; 
      }

      me .floating-select {
        font-size:14px;
        padding:4px 4px;
        display:block;
        width:100%;
        height:30px;
        background-color: transparent;
        border:none;
        border-bottom:1px solid #757575;
      }

      me .floating-select:focus {
          outline:none;
          border-bottom:2px solid #5264AE; 
      }

      me label {
        color:#999; 
        font-size:14px;
        font-weight:normal;
        position:absolute;
        pointer-events:none;
        left:5px;
        top:5px;
        transition:0.2s ease all; 
        -moz-transition:0.2s ease all; 
        -webkit-transition:0.2s ease all;
      }

      me .floating-select:focus ~ label , .floating-select:not([value=""]):valid ~ label {
        top:-18px;
        font-size:14px;
        color:#5264AE;
      }

      /* active state */
      me .floating-select:focus ~ .floating-select:focus ~  {
        width:50%;
      }

      *, *:before, *:after {
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
      }

      /* active state */
      me .floating-select:focus ~  {
        -webkit-animation:inputHighlighter 0.3s ease;
        -moz-animation:inputHighlighter 0.3s ease;
        animation:inputHighlighter 0.3s ease;
      }
  </style>
  <div class="floating-label">
    <select class="floating-select">
      <option value=""></option>
      {% for option in optionslist %}
        {% if option is mapping %}
          <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
        {% else %}
          <option value="{{ option }}">{{ option }}</option>
        {% endif %}
      {% endfor %}
    </select>
    <label>{{ label }}</label>
  </div>

</div>
