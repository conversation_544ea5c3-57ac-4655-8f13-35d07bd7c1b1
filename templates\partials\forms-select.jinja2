<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 65px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me select {
          all: unset;
          padding-right: 15px;
          border-bottom: 1px solid;
          border-bottom-color: var(--color-input-lines);
          background: url(/static/images/icons8-arrow-down-64.png) no-repeat right var(--color-background-bright);
          height: 28px;
          background-repeat: no-repeat;
          background-size: 26px 24px;
      }
  </style>
  <div class="floating-label">
    <select class="floating-select">
      <option value=""></option>
      {% for option in optionslist %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
    </select>
    <label>{{ titledisabled }}</label>
  </div>

</div>
