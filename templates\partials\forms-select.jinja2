<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 94px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me select {
          font-family: 'Noto Sans', sans-serif;
          font-size: 14px;
          color: black;
          height: 29px;
          border: 0;
          z-index: 1;
          background-color: transparent;
          border-bottom: 1px solid var(--color-input-lines);
          font-size: 1.125rem;
          background: url(/static/images/icons8-arrow-down-64.png) no-repeat right var(--color-background-bright);
          background-size: 26px 24px;
          padding-right: 30px;

          &:focus {
              outline: 0;
              border-bottom: 1px solid var(--color-input-lines);

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-input-lines);
                  transform: translateY(-1.5rem);
              }
          }

          &:valid:not([value=""]) {
              border-bottom: 1px solid hsl(55, 96%, 38%);

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-input-lines);
                  transform: translateY(-1.5rem);
              }
          }
      }
  </style>
  <select onchange="this.setAttribute('value', this.value);" value="">
    <option value=""></option>
    {% for option in optionslist %}
      <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
    {% endfor %}
  </select>
  <label class="input-label">
    <style>
        me {
            color: black;
            position: absolute;
            transition: .15s ease;
            margin-bottom: 6px;
        }
    </style>
    {{ label | safe }}
  </label>

</div>

<script>
  // Initialize select value attribute for proper label positioning
  document.addEventListener("DOMContentLoaded", function() {
    const select = document.currentScript.previousElementSibling.querySelector('select');
    if (select) {
      select.setAttribute('value', select.value);
    }
  });
</script>
