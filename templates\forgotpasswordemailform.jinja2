{% extends "base.jinja2" %}

{% block title %}Forgot Password{% endblock %}
{% block content %}
<div id="content-div">

    {# TITLE DOUBLE #}
    <div>
      <style>
          me {
              width: 100%;
              text-align: center;
              padding-bottom: 8px;
          }

          me hr {
              display: block;
              height: 1px;
              border: 0;
              border-top: 1px solid #a8a8a8;
              margin-top: 10;
              padding: 0;
          }
      </style>
      <div>
        Reset Password
        <style>
            me {
                padding-bottom: 28px;
                font-family: 'Noto Serif', serif;
                font-size: 30px;
                font-weight: 400;
                font-stretch: semi-condensed;
                font-style: italic;
            }
        </style>
      </div>
      <div>
        <style>
            me {
                padding-bottom: 2px;
                font-family: "Noto Sans", sans-serif;
                font-size: 20px;
                font-weight: 300;
                font-stretch: semi-condensed;
                font-style: normal;
            }
        </style>
        The password reset link will be sent via email, so the email must be active and match the one registered. 
      </div>
      <hr />
    </div>

  {# method="post" action="/users/login" #}
  <form data-signals="{_users_submit_button_disable:false}" data-on-submit="$_users_submit_button_disable = true;@post('/forgotpassword_sendemail', {contentType: 'form'})">
    {{ render_partial('partials/forms-input.jinja2', namealwayschange='forgotpasswordemail', label='Email', type = 'email', pattern="^[^@]+@[^@]+\.[^@]+$", errormessage="Invalid email address") }}


    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_users_submit_button_disable">
      <style>
          me {
              height: 40px;
              margin-top: 3.6rem;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-text-dark);
              border: 1px solid var(--color-text-dark);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
          }

          me:hover {
              background-color: var(--color-background-dark);
              color: var(--color-text-bright);
          }

          me:disabled {
              background-color: rgb(72, 72, 72);
              opacity: 0.4;
              padding: 0px 0px;
          }

          me:disabled .button-spinner {
              display: inline;
              margin-top: 4px;
              margin-bottom: 0px;
          }

          me:disabled .button-text {
              display: none;
          }

          me .button-spinner {
              display: none;
              width: 30px;
              height: 30px;
          }
      </style>
      <span class="button-text">Send Reset Link</span>
      <img class="button-spinner"
            src="{{ url_for('static', path='/images/tube-spinner.svg') }}"
            alt="spinning..." />
    </button>

  </form>

  <div id="errordiv"></div>

  <div>
    <style>
        me {
            margin-top: 99px;
            margin-bottom: 0px;
            text-align: center;
        }

        me a {
            color: var(--color-text-dark);
            text-decoration: none;
            font-weight: 400;
            font-stretch: semi-condensed;
        }

        me a:hover {
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
    Don't have an account yet?
    <a href="/signup_form">Sign up</a>
  </div>

</div>

{% endblock content %}

